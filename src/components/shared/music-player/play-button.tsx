"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import { useMusicPlayer } from "@/contexts/music-player-context/music-player-context"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  Volume1,
  VolumeX,
  Maximize2,
  Minimize2,
  ExternalLink,
} from "lucide-react"
import { cn } from "@/lib/utils"

export function MusicPlayer() {
  const {
    currentSong,
    isPlaying,
    volume,
    progress,
    duration,
    isExpanded,
    togglePlay,
    setVolume,
    seekTo,
    nextSong,
    prevSong,
    toggleExpanded,
  } = useMusicPlayer()

  const [showVolumeSlider, setShowVolumeSlider] = useState(false)
  const volumeRef = useRef<HTMLDivElement>(null)

  // Format time in MM:SS
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`
  }

  // Handle click outside volume slider
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (volumeRef.current && !volumeRef.current.contains(event.target as Node)) {
        setShowVolumeSlider(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Volume icon based on level
  const VolumeIcon = volume === 0 ? VolumeX : volume < 0.5 ? Volume1 : Volume2

  if (!currentSong) return null

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 bg-background border-t z-50 transition-all duration-300",
        isExpanded ? "h-[300px]" : "h-16",
      )}
    >
      {/* Mini Player (Always visible) */}
      <div className="h-16 px-4 flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={togglePlay}
            aria-label={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
          </Button>

          <div className="flex items-center gap-3 cursor-pointer" onClick={toggleExpanded}>
            {!isExpanded && (
              <div className="w-8 h-8 relative rounded overflow-hidden">
                <Image
                  src={currentSong.albumArt || "/placeholder.svg"}
                  alt={currentSong.album}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div>
              <p className="font-medium text-sm line-clamp-1">{currentSong.title}</p>
              <p className="text-xs text-muted-foreground line-clamp-1">{currentSong.artist}</p>
            </div>
          </div>
        </div>

        <div className="flex-1 hidden md:flex items-center">
          <div className="w-full flex items-center gap-2">
            <span className="text-xs text-muted-foreground w-10 text-right">{formatTime(progress)}</span>
            <Slider
              value={[progress]}
              max={duration}
              step={1}
              className="flex-1"
              onValueChange={(value) => seekTo(value[0])}
            />
            <span className="text-xs text-muted-foreground w-10">{formatTime(duration)}</span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative" ref={volumeRef}>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => setShowVolumeSlider(!showVolumeSlider)}
              aria-label="Volume"
            >
              <VolumeIcon className="h-5 w-5" />
            </Button>
            {showVolumeSlider && (
              <div className="absolute bottom-full right-0 mb-2 p-3 bg-background border rounded-md shadow-lg w-36">
                <Slider value={[volume]} max={1} step={0.01} onValueChange={(value) => setVolume(value[0])} />
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={toggleExpanded}
            aria-label={isExpanded ? "Minimize player" : "Expand player"}
          >
            {isExpanded ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      {/* Expanded Player */}
      {isExpanded && (
        <div className="p-6 h-[calc(300px-4rem)] flex flex-col md:flex-row gap-6">
          <div className="flex-shrink-0">
            <div className="relative w-48 h-48 rounded-lg overflow-hidden shadow-lg">
              <Image
                src={currentSong.albumArt || "/placeholder.svg"}
                alt={currentSong.album}
                fill
                className="object-cover"
              />
            </div>
          </div>

          <div className="flex-1 flex flex-col justify-between">
            <div>
              <h3 className="text-2xl font-bold">{currentSong.title}</h3>
              <p className="text-lg text-muted-foreground">{currentSong.artist}</p>
              <p className="text-sm text-muted-foreground mt-1">{currentSong.album}</p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-center gap-4">
                <Button variant="ghost" size="icon" onClick={prevSong} aria-label="Previous song">
                  <SkipBack className="h-6 w-6" />
                </Button>
                <Button
                  size="icon"
                  className="h-12 w-12 rounded-full"
                  onClick={togglePlay}
                  aria-label={isPlaying ? "Pause" : "Play"}
                >
                  {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                </Button>
                <Button variant="ghost" size="icon" onClick={nextSong} aria-label="Next song">
                  <SkipForward className="h-6 w-6" />
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground w-10 text-right">{formatTime(progress)}</span>
                <Slider
                  value={[progress]}
                  max={duration}
                  step={1}
                  className="flex-1"
                  onValueChange={(value) => seekTo(value[0])}
                />
                <span className="text-xs text-muted-foreground w-10">{formatTime(duration)}</span>
              </div>
            </div>

            <div className="space-y-2 mt-4">
              <h4 className="text-sm font-medium">Credits:</h4>
              <div className="text-xs text-muted-foreground">
                {currentSong.credits?.producer && <p>Producer: {currentSong.credits.producer}</p>}
                {currentSong.credits?.writer && <p>Writer: {currentSong.credits.writer}</p>}
                {currentSong.credits?.engineer && <p>Engineer: {currentSong.credits.engineer}</p>}
              </div>
              <div className="flex justify-between items-center mt-2">
                <p className="text-xs text-muted-foreground">Audio Source: MusicBrainz</p>
                <Button variant="outline" size="sm" className="gap-1">
                  <ExternalLink className="h-3 w-3" />
                  <span className="text-xs">View Full Song Page</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
