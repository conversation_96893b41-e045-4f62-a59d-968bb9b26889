import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import React from "react";
import { ColumnDef } from "@tanstack/react-table";

export type CreditRow = {
  id: string;
  title?: string | null;
  duration?: number | null;
  releaseDate?: string | null;
  type?: "song" | "recording";
};

export type CreditTableProps = {
  rows: CreditRow[];
  onRowClick: (row: CreditRow) => void;
  onClose: () => void;
};

export const CreditTable: React.FC<CreditTableProps> = ({
  rows,
  onRowClick,
  onClose,
}) => {
  const columns: ColumnDef<CreditRow>[] = [
    {
      accessorKey: "title",
      header: () => <span className="cursor-pointer select-none">Name</span>,
      cell: (info) => {
        const value = info.getValue();
        return value ? <span className="font-medium">{String(value)}</span> : <span className="font-medium">-</span>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "duration",
      header: "Duration",
      cell: (info) => {
        const duration = info.getValue();
        return typeof duration === "number"
          ? `${Math.floor(duration / 1000 / 60)}:${String(Math.floor((duration / 1000) % 60)).padStart(2, "0")}`
          : "-";
      },
      enableSorting: true,
    },
    {
      accessorKey: "releaseDate",
      header: () => <span className="cursor-pointer select-none">Release Date</span>,
      cell: (info) => {
        const date = info.getValue();
        return date ? new Date(date as string).toLocaleDateString() : "-";
      },
      enableSorting: true,
    },
  ];

  return (
    <div className="overflow-x-auto">
      <DataTable
        columns={columns}
        data={rows}
        onRowClick={onRowClick}
        className="min-w-full border rounded-lg"
      />
      <div className="flex justify-end mt-2">
        <Button
          variant="secondary"
          className="ml-auto"
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
        >
          Close <X className="ml-1" />
        </Button>
      </div>
    </div>
  );
};
